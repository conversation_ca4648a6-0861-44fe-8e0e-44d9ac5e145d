#ifndef __FIM_COMMON_H__
#define __FIM_COMMON_H__

#include <vmlinux.h>
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_tracing.h>
#include <bpf/bpf_core_read.h>
#include "../include/fim_shared.h"

/* eBPF Map定义 */
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, FIM_MAX_WHITELIST);
    __type(key, struct fim_inode_key);
    __type(value, struct fim_policy_entry);
} policy_map SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_RINGBUF);
    __uint(max_entries, 256 * 1024);
} events_map SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_ARRAY);
    __uint(max_entries, 1);
    __type(key, __u32);
    __type(value, struct fim_stats);
} stats_map SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_ARRAY);
    __uint(max_entries, 1);
    __type(key, __u32);
    __type(value, struct fim_config);
} config_map SEC(".maps");

/* 辅助函数声明 */
static inline int get_file_path(struct file *file, char *buf, int buf_size);
static inline int check_inode_policy(struct inode *inode, __u32 operation, __u32 *rule_type);
static inline int check_policy_or_parents(struct dentry *de, __u32 operation, __u32 *rule_type);
static inline int make_access_decision(struct dentry *de, __u32 operation, struct fim_config *config);
static inline void log_event(__u32 operation, __u32 decision, struct inode *inode, const char *path);
static inline void update_stats(__u32 decision, int policy_hit);
static inline struct fim_config *get_config(void);
static inline __u32 get_device_id(struct inode *inode);

/* 获取文件完整路径 */
static inline int get_file_path(struct file *file, char *buf, int buf_size) {
    struct path *path_ptr;
    struct dentry *dentry;
    struct dentry *parent;
    char *p = buf + buf_size - 1;
    int len = 0;
    
    if (!file || !buf || buf_size <= 0)
        return -1;
    
    *p = '\0';
    
    path_ptr = &file->f_path;
    dentry = BPF_CORE_READ(path_ptr, dentry);
    
    /* 从叶子节点向根节点遍历构建路径 */
    for (int i = 0; i < 32 && dentry; i++) { /* 限制循环次数防止无限循环 */
        struct qstr d_name;
        const char *name;
        int name_len;
        
        BPF_CORE_READ_INTO(&d_name, dentry, d_name);
        name = BPF_CORE_READ(d_name, name);
        name_len = BPF_CORE_READ(d_name, len);
        
        if (name_len <= 0 || name_len > 255)
            break;
            
        /* 检查缓冲区空间 */
        if (len + name_len + 1 >= buf_size)
            break;
            
        /* 向前移动指针并复制名称 */
        p -= name_len;
        len += name_len;
        bpf_probe_read_kernel_str(p, name_len + 1, name);
        
        /* 添加路径分隔符 */
        if (len < buf_size - 1) {
            p--;
            *p = '/';
            len++;
        }
        
        parent = BPF_CORE_READ(dentry, d_parent);
        if (parent == dentry) /* 到达根目录 */
            break;
        dentry = parent;
    }
    
    /* 将结果移动到缓冲区开始位置 */
    if (len > 0 && p != buf) {
        bpf_probe_read_kernel_str(buf, len + 1, p);
    } else if (len == 0) {
        buf[0] = '/';
        buf[1] = '\0';
        len = 1;
    }
    
    return len;
}

/* 获取设备ID */
static inline __u32 get_device_id(struct inode *inode) {
    if (!inode)
        return 0;
    return BPF_CORE_READ(inode, i_sb, s_dev);
}

/* 检查单个inode的策略规则 */
static inline int check_inode_policy(struct inode *inode, __u32 operation, __u32 *rule_type) {
    struct fim_inode_key key;
    struct fim_policy_entry *entry;

    if (!inode)
        return 0;

    key.inode = BPF_CORE_READ(inode, i_ino);
    key.dev = get_device_id(inode);

    entry = bpf_map_lookup_elem(&policy_map, &key);
    if (entry && (entry->operations & (1 << operation))) {
        if (rule_type) {
            *rule_type = entry->rule_type;
        }
        return 1;
    }

    return 0;
}

/* 递归检查inode或其父目录的策略规则 */
static inline int check_policy_or_parents(struct dentry *de, __u32 operation, __u32 *rule_type) {
    struct dentry *cur = de;

    for (int i = 0; i < 6; i++) {
        struct inode *inode;

        if (!cur)
            break;

        inode = BPF_CORE_READ(cur, d_inode);
        if (!inode)
            break;

        /* 检查当前inode */
        if (check_inode_policy(inode, operation, rule_type))
            return 1;

        /* 检查是否到达根目录 */
        if (cur == BPF_CORE_READ(cur, d_parent))
            break;

        cur = BPF_CORE_READ(cur, d_parent);
    }

    return 0;
}

/* 根据策略做出访问决策 */
static inline int make_access_decision(struct dentry *de, __u32 operation, struct fim_config *config) {
    __u32 rule_type = 0;
    int has_rule = check_policy_or_parents(de, operation, &rule_type);

    /* 策略模式：根据具体规则类型决定 */
    if (has_rule) {
        return (rule_type == FIM_RULE_ALLOW) ? FIM_ALLOW : FIM_DENY;
    } else {
        return config->default_action;
    }
}

/* 记录事件日志 */
static inline void log_event(__u32 operation, __u32 decision, struct inode *inode, const char *path) {
    struct fim_event *event;
    __u64 pid_tgid = bpf_get_current_pid_tgid();
    __u64 uid_gid = bpf_get_current_uid_gid();

    event = bpf_ringbuf_reserve(&events_map, sizeof(*event), 0);
    if (!event)
        return;

    event->timestamp = bpf_ktime_get_ns();
    event->pid = pid_tgid >> 32;
    event->uid = uid_gid & 0xFFFFFFFF;
    event->gid = uid_gid >> 32;
    event->operation = operation;
    event->decision = decision;

    /* 记录inode信息 */
    if (inode) {
        event->inode = BPF_CORE_READ(inode, i_ino);
        event->dev = get_device_id(inode);
    } else {
        event->inode = 0;
        event->dev = 0;
    }

    bpf_get_current_comm(event->comm, sizeof(event->comm));
    if (path) {
        bpf_probe_read_kernel_str(event->path, sizeof(event->path), path);
    } else {
        event->path[0] = '\0';
    }

    bpf_ringbuf_submit(event, 0);
}

/* 更新统计信息 */
static inline void update_stats(__u32 decision, int policy_hit) {
    __u32 key = 0;
    struct fim_stats *stats;

    stats = bpf_map_lookup_elem(&stats_map, &key);
    if (!stats)
        return;

    __sync_fetch_and_add(&stats->total_events, 1);

    if (decision == FIM_ALLOW) {
        __sync_fetch_and_add(&stats->allowed_events, 1);
    } else {
        __sync_fetch_and_add(&stats->denied_events, 1);
    }

    if (policy_hit) {
        __sync_fetch_and_add(&stats->whitelist_hits, 1);
    } else {
        __sync_fetch_and_add(&stats->whitelist_misses, 1);
    }
}

/* 获取配置 */
static inline struct fim_config *get_config(void) {
    __u32 key = 0;
    return bpf_map_lookup_elem(&config_map, &key);
}

#endif /* __FIM_COMMON_H__ */
